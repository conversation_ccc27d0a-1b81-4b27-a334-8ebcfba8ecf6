//* PACKAGES
import React, { useState, useEffect } from 'react';
import { router, useForm, usePage } from "@inertiajs/react";

//* ICONS
//...

//* LAYOUTS
import UserLayout from "@/Layouts/UserLayout";

//* COMPONENTS
import AppVerificationPromptGroup from "@/Components/App/AppVerificationPromptGroupComponent";
import UserDomainList from "@/Components/Domain/Index/UserDomainList";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import _DomainStatus from "@/Constant/_DomainStatus";
import DomainFilter from "@/Components/Domain/Index/DomainFilter";
import DomainStatusNav from "@/Components/Domain/Index/DomainStatusNav";
import DomainActionModal from "@/Components/Domain/Index/DomainActionModal";
import DomainSelectedItemsModal from "@/Components/Domain/Index/DomainSelectedItemsModal";

//* STATE
import CartCounterState from "@/State/CartCounterState";

//* UTILS
import useEchoChannel from "@/Util/useEchoChannel";
import UtilCheckIfHasSecuredTransaction from "@/Util/UtilCheckIfHasSecuredTransaction";

//* CONSTANT
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
import PartialDomainModalFormExport from '@/Pages/Domain/Partials/PartialModalDomainFormExport';
import PartialButtonActionsGroup from '@/Pages/Domain/Partials/PartialButtonActionsGroup';
import LoaderSpinner from '@/Components/LoaderSpinner';
import DomainSearchModal from '@/Components/Domain/Index/DomainSearchModal';
import { toast } from 'react-toastify';

export default function Index(
    {
        items = [],
        onFirstPage,
        onLastPage,
        nextPageUrl,
        previousPageUrl,
        categories,
        status_type,
        cartCount,
        itemCount = 0,
        total = 0,
        itemName = "domain",
        searchedDomains = [],
    }
) {
    searchedDomains = searchedDomains.join('\n');
    //! PACKAGE
    const user = usePage().props.auth.user;
    const params = route().params;
    const paramStatus = params.status ?? "All";

    //! STATES
    const [stateShowVerificationPrompt, setStateShowVerificationPrompt] = useState(false);
    const [showDomainActionModal, setShowDomainActionModal] = useState(false);
    const [showDomainSelectedItemsModal, setShowDomainSelectedItemsModal] = useState(false);
    const [showDomainSearchModal, setShowDomainSearchModal] = useState(false);
    const [showModalDomainExportForm, setShowModalDomainExportForm] = useState(false);
    const [modalDomainExportFormTypeMode, setModalDomainExportFormTypeMode] = useState('all');
    const [hasLockedDomain, setHasLockedDomain] = useState(false);
    const [requestAuthcodeData, setRequestAuthcodeData] = useState({});
    const [selectedItems, setSelectedItems] = useState([]);
    const [selectedIds, setSelectedIds] = useState([]);
    const [hasSpinner, setSpinner] = useState(false);
    const { cartCounter, setCartCounter } = CartCounterState();
    const [hasDomainSearch, setHasDomainSearch] = useState(searchedDomains.length > 0);

    const { data, setData, post, processing, errors } = useForm({
        filters: params,
        domains: "",
    });

    //! VARIABLES
    const shouldVerifyUser = UtilCheckIfHasSecuredTransaction('domainAuthCode');

    useEchoChannel(
        `UpdateDomainsTable.${user.id}`,
        '.update.domains.table',
        () => router.get(route('domain'), params, { replace: true, preserveState: true, preserveScroll: true })
    );

    function handleSubmitDomainSearch() {
        post(route("search.domain"), {
            onStart: () => {
                toggleDomainSearchModal();
                setHasDomainSearch(false);
                toast.info("Searching domains, please wait...");
            },
            onSuccess: () => {
                setHasDomainSearch(true);
                toast.success("Search complete!");
            },
            onError: () => {
                toast.error("Error searching domains.");
                toggleDomainSearchModal();
                setHasDomainSearch(false);
            },
        });
    }

    function handleCheckAll(e) {
        if (e.currentTarget.checked) {
            //! If checked, add ids from items that are not in selectedItems
            const updatedSelectedItems = [...selectedItems];
            const updatedSelectedIds = [...selectedIds];

            items.forEach(
                (item) => {
                    if (
                        !updatedSelectedItems.some(selectedItem => selectedItem.id === item.id)
                        && (item.status == 'ACTIVE' || item.status == 'EXPIRED' || item.status == 'REDEMPTION')
                    ) {
                        updatedSelectedItems.push(item);
                    }
                }
            );

            items.forEach(
                (item) => {
                    if (
                        !updatedSelectedIds.includes(item.id)
                        && (item.status == 'ACTIVE' || item.status == 'EXPIRED' || item.status == 'REDEMPTION')
                    ) {
                        updatedSelectedIds.push(item.id);
                    }
                }
            );

            setSelectedItems(updatedSelectedItems);
            setSelectedIds(updatedSelectedIds);
        }
        else {
            //! If unchecked, remove ids from items that are in selectedItems
            const updatedSelectedItems = selectedItems.filter(item => !items.some(i => i.id === item.id));
            const updatedSelectedIds = selectedIds.filter(id => !items.some(item => item.id === id));

            setSelectedItems(updatedSelectedItems);
            setSelectedIds(updatedSelectedIds);
        }
    }

    function handleCheckRow(itemId, item, checked) {
        if (checked) {
            setSelectedIds((prevSelectedItems) => [...prevSelectedItems, itemId]);
            setSelectedItems((prevSelected) => [...prevSelected, item]);
        }
        else {
            setSelectedIds((prevSelectedItems) => prevSelectedItems.filter((id) => id !== itemId));
            setSelectedItems((prevSelected) => prevSelected.filter((i) => i.id !== itemId));
        }

    };

    const toggleDomainSearchModal = () => {
        setShowDomainSearchModal(prev => !prev);
    };

    //! REQUEST AUTH CODE FUNCTIONS
    function handleDomainActionModal(shouldSubmit = false) {
        setShowDomainActionModal(false);

        if (shouldSubmit == false) {
            //...
        }
        else {
            if (shouldVerifyUser == true) {
                setStateShowVerificationPrompt(true);
            }
            else {
                handleRequestAuthCodeSubmit();
            }
        }

    };

    function handleRequestAuthCodeSubmit() {
        setShowDomainActionModal(false);
        setStateShowVerificationPrompt(false);

        router.post(
            route("domain.request-authcode"),
            requestAuthcodeData
        );
    }

    const handleRequestAuthcodeBtn = (selectedDomains) => {
        setRequestAuthcodeData({ domains: selectedDomains });

        //! Check for locked domains
        let hasLocked = false;

        selectedDomains.forEach(
            (item) => {
                if (
                    item.client_status &&
                    item.client_status.includes(_DomainStatus.STATUS.TRANSFER)
                ) {
                    hasLocked = true;
                    return;
                }
            }
        );

        setHasLockedDomain(hasLocked);
        setShowDomainActionModal(true);
    };

    const initializeData = () => {
        setCartCounter(cartCount);
    };

    //! USE EFFECT
    useEffect(() => {
        initializeData();
    },
        []
    );

    router.on("start", () => {
        setSpinner(true);
    });

    router.on("finish", () => {
        setSpinner(false);
    });

    const query = route().params;
    const limit = parseInt(query.limit) || 20;

    const handleLimitChange = (e) => {
        router.get(
            route("domain"),
            {
                ...route().params, // preserve existing query params
                limit: e.target.value,
                page: 1,
            },
            {
                preserveState: true,
                preserveScroll: true,
                replace: true,
            }
        );
        console.log(items);
    };

    return (
        <UserLayout
            postRouteName={"domain"}
        >
            <AppVerificationPromptGroup
                isShow={stateShowVerificationPrompt}
                onClose={() => setStateShowVerificationPrompt(false)}
                onSubmitSuccess={handleRequestAuthCodeSubmit}
                onSubmitError={() => { }}
            />
            <div
                className="mx-auto container max-w-[1000px] mt-20 flex flex-col space-y-4"
            >
                <PartialButtonActionsGroup
                    selectedIds={selectedIds}
                    selectedItems={selectedItems}
                    handleRequestAuthcodeBtn={handleRequestAuthcodeBtn}
                    user={user}
                    itemCount={itemCount}
                    handleModalDomainFormExportShow={
                        (mode) => {
                            setShowModalDomainExportForm(true);
                            setModalDomainExportFormTypeMode(mode);
                        }
                    }
                />
                <div className="flex justify-start">
                    <label className="mr-2 text-sm pt-1 text-gray-600">
                        Show
                    </label>
                    <select
                        value={limit}
                        onChange={handleLimitChange}
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
                        {[20, 25, 30, 40, 50, 100].map((val) => (
                            <option key={val} value={val}>
                                {val}
                            </option>
                        ))}
                    </select>
                </div>
                <DomainFilter
                    params={route().params}
                    categories={categories}
                    status_type={status_type}
                    items={items}
                    toggleDomainSearchModal={toggleDomainSearchModal}
                    hasDomainSearch={hasDomainSearch}
                    setHasDomainSearch={setHasDomainSearch}
                    setDomainSearchValue={setData}
                    searchedDomains={searchedDomains}
                    selectedIds={selectedIds}
                    handleSelectItemsBtn={() => { setShowDomainSelectedItemsModal(true); }}
                />

                <DomainStatusNav
                    status_type={status_type}
                    params={route().params}
                    paramStatus={paramStatus}
                />
                {hasSpinner ?
                    <div className="mx-auto container mt-16 flex flex-col px-28 rounded-lg"><LoaderSpinner ml='ml-72' h='h-12' w='w-12' position='absolute' /><br /><span className="relative top-9 left-20 ml-44">Loading Data...</span></div>
                    :
                    <UserDomainList
                        items={items}
                        selectedIds={selectedIds}
                        params={route().params}
                        handleRequestAuthcodeBtn={handleRequestAuthcodeBtn}
                        handleCheckRow={handleCheckRow}
                        handleCheckAll={handleCheckAll}
                    />
                }

                <CursorPaginate
                    onFirstPage={onFirstPage}
                    onLastPage={onLastPage}
                    nextPageUrl={nextPageUrl}
                    previousPageUrl={previousPageUrl}
                    itemCount={itemCount}
                    total={total}
                    itemName={itemName}
                    shouldPreserveState={true}
                />

                <DomainActionModal
                    requestAuthcodeData={requestAuthcodeData}
                    user={user}
                    hasLockedDomain={hasLockedDomain}
                    isModalOpen={showDomainActionModal}
                    closeModal={handleDomainActionModal}
                />

                <DomainSelectedItemsModal
                    selectedItems={selectedItems}
                    isModalOpen={showDomainSelectedItemsModal}
                    closeModal={() => { setShowDomainSelectedItemsModal(false); }}
                />

                <DomainSearchModal
                    isModalOpen={showDomainSearchModal}
                    closeModal={toggleDomainSearchModal}
                    searchedDomains={searchedDomains}
                    domainSearchValue={data.domains}
                    errors={errors}
                    processing={processing}
                    setDomainSearchValue={setData}
                    handleSubmitDomainSearch={handleSubmitDomainSearch}
                />

                <PartialDomainModalFormExport
                    selectedItems={selectedIds}
                    tableFilters={route().params}
                    formMode={modalDomainExportFormTypeMode}
                    itemsCountTotal={total}
                    isModalOpen={showModalDomainExportForm}
                    handleModalClose={() => setShowModalDomainExportForm(false)}
                />
            </div>
        </UserLayout>
    );
}
