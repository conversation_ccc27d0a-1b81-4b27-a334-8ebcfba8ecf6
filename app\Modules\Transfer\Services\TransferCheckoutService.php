<?php

namespace App\Modules\Transfer\Services;

use App\Modules\AccountCredit\Services\AccountCreditService;
use App\Modules\Cart\Services\CheckoutCartService;
use App\Modules\Client\Jobs\ScheduleDomainExpiryNotice;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Epp\Constants\RegistryTransactionType;
use App\Modules\Epp\Services\RegistryAccountBalanceService;
use App\Modules\Payment\Services\PaymentFeeService;
use App\Modules\PaymentMethod\Services\PaymentMethodService;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Stripe\Helpers\StripeFeeHelper;
use App\Modules\Stripe\Services\StripeLimiter;
use App\Traits\UserContact;
use App\Util\Constant\RateLimiterKey;
use App\Util\Helper\RateLimit;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class TransferCheckoutService
{
    use UserContact, UserLoggerTrait;

    private $maxAttemptError = 3;

    private $dispatchDelayInSeconds = 180; // three minutes

    public static function instance()
    {
        $transferCheckoutService = new self;

        return $transferCheckoutService;
    }

    public function getTransferCheckoutSummary()
    {
        $transferData = $this->getTransferCheckoutData();
        $otherFeesData = $transferData['other_fees'];
        $stripeFeeObj = StripeFeeHelper::calculateTransactionFee($otherFeesData['bill_total'] ?? 0);
        $setupIntents = TransferCheckoutPaymentService::instance()->setPaymentDetails($stripeFeeObj['gross_amount'] ?? $otherFeesData['bill_total']);
        $accountCredit = AccountCreditService::instance()->getLatestBlock($this->getUserId());
        $primaryPaymentMethod= (new PaymentMethodService())->fetchPrimaryPaymentMethod($this->getUserId());
        $default_card = (new PaymentMethodService())->fetchDefaultCard($this->getUserId());

        $data['other_fees'] = $otherFeesData;
        $data['secret'] = $setupIntents->client_secret;
        $data['intent'] = $setupIntents->id;
        $data['promise'] = Config::get('stripe.publishable_key');
        $data['account_credit_balance'] = $accountCredit->running_balance ?? 0;
        $data['stripeFeeObj'] = $stripeFeeObj;

        $data['domains'] = $transferData;
        $data['primary_payment_method'] = $primaryPaymentMethod;
        $data['default_card'] = $default_card;

        // Pass balance error to frontend if present
        if (isset($transferData['balance_error']) && $transferData['balance_error']['error']) {
            $data['balance_error'] = $transferData['balance_error'];
        }

        return $data;
    }

    public function store(array $request)
    {
        StripeLimiter::instance()->clearAttempt();

        $createdDomains = $this->createRegisteredDomains();
        $this->createTransferDomains($createdDomains);
        $summaryId = TransferCheckoutPaymentService::instance()->createPaymentSummary($request, $createdDomains);

        if ($createdDomains) {
            $domainAuthCodes = $this->getAuthCodes($request);
            $this->callEppTransferRequestJob($domainAuthCodes, $createdDomains);
        }

        ScheduleDomainExpiryNotice::dispatch($this->getUserId())->delay($this->dispatchDelayInSeconds);

        return $summaryId ?? 0;
    }

    public function authenticateUser(string $password): void
    {
        $canTry = RateLimit::attempt(RateLimiterKey::authAttempt(auth()->id()));

        if (! $canTry) {
            throw ValidationException::withMessages(['message' => 'Maximum attempt, please try again after a few minutes']);
        }

        if (! Hash::check($password, auth()->user()->getAuthPassword())) {
            throw ValidationException::withMessages(['message' => 'Invalid user password, please try again.']);
        }

        RateLimit::clear(RateLimiterKey::authAttempt(auth()->id()));
    }

    // PRIVATE Functions

    public function checkRegistryBalance(array $request)
    {
        $domains = $request['domains']['domains'] ?? [];

        if (! empty($domains)) {
            $result = RegistryAccountBalanceService::checkRegistryBalance($domains, $request['domains']['other_fees'], FeeType::TRANSFER, $request['intent'] ?? '');
            if ($result['error']) {
                throw new \Exception($result['message']);
            }
            $this->creditRegistryAccountBalance($result['registryBalance'], RegistryTransactionType::SUB_FUND, RegistryTransactionType::DOMAIN_TRANSFER);
        }
    }

    private function creditRegistryAccountBalance(array $registryBalance, $transactionType, $description): void
    {
        foreach ($registryBalance as $balance) {
            RegistryAccountBalanceService::credit(
                $balance['balance'],
                $balance['amount'],
                $transactionType,
                $description
            );
        }
    }

    private function createRegisteredDomains()
    {
        $insertedDomains = TransferDomainCreateService::instance()->storeDomains();
        if (! $insertedDomains) {
            return null;
        }

        return TransferDomainCreateService::instance()->storeRegisteredDomains($insertedDomains, UserDomainStatus::RESERVED);
    }

    private function callEppTransferRequestJob(array $domainAuthCodes, array $createdDomains)
    {
        TransferDomainCreateService::instance()->requestTransferToEpp($domainAuthCodes, $createdDomains);
    }

    private function getAuthCodes(array $request): array
    {
        $domains = $request['domains']['domains'];
        $authCodes = array_column($domains, 'auth_code', 'name');

        return $authCodes;
    }

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }

    private function getTransferCheckoutData()
    {
        $data = TransferCartService::instance()->getTransferCartData();

        if (empty($data['domains'])) {
            return [];
        }

        $other_fees = PaymentFeeService::getOtherTransferFees($data['domains'], $data['settings']['transfer_fees']);
        $balanceResult = RegistryAccountBalanceService::checkRegistryBalance($data['domains'], $other_fees, FeeType::TRANSFER);
        
        if ($balanceResult['error']) {
            $data['balance_error'] = $balanceResult;
        }

        // Check account credit balance if not already errored
        if (!isset($data['balance_error'])) {
            $accountCreditResult = CheckoutCartService::instance()->checkAccountCreditBalance($other_fees['bill_total']);
            if ($accountCreditResult['error']) {
                $data['balance_error'] = $accountCreditResult;
            }
        }

        $data['other_fees'] = $other_fees;

        return $data;
    }

    private function createTransferDomains(array $createdDomains)
    {
        if (! $createdDomains) {
            return null;
        }

        $registeredDomains = $createdDomains['registered_domains'];

        return TransferDomainCreateService::instance()->storeTransferDomains($registeredDomains);
    }
}