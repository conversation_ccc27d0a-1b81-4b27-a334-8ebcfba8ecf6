<?php

use App\Console\Commands\Constants\SchedulerTypes;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Carbon\Carbon;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cron_run_histories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->boolean('has_data')->default(false);
            $table->timestamp('last_run_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cron_run_histories');
    }
};
