import PrimaryButton from "@/Components/PrimaryButton";
import CartCounterState from "@/State/CartCounterState";
import { useState } from "react";
import { toast } from "react-toastify";
import SearchDomainNow from "./SearchDomainNow";
import SearchNoDomainFound from "./SearchNoDomainFound";
import axios from "axios";
import { isInteger } from "lodash";
import OfferPopUp from "./components/OfferPopUp";

export default function SearchResults({ auth, searchResult, cart, setCart, fees, modal, showModal, offer, showOffer, offerPrice }) {

    const { setCartCounter } = CartCounterState();

    const [isLoading, setIsLoading] = useState(false);

    const storeItemToCart = async (payload) => {
        axios.post(route("mycart.store"), payload)
            .then((response) => {
                setIsLoading(false);
                toast.success('Added to cart.');
                setCart([...cart, payload[0].name]);
                setCartCounter(cart.length + 1);
            })
            .catch((error) => {
                setIsLoading(false);
                setCart([...cart, payload[0].name]);
                toast.error(error.response.data.message ?? 'Something went wrong!');
            })
    };

    const onhandleAddToCart = (name) => {
        storeItemToCart([
            {
                name: name,
                extension: name.replace(/^[^\.]*\./, ""),
            },
        ]);
    };

    const getDomainPrice = (domain) => {
        const extension = (domain.name).split('.').pop();

        if(!domain.in_afternic) return `$${(fees[extension].price ?? 0).toLocaleString('en', { useGrouping: true })}`;

        if(domain.price <= 0 && domain.min_offer > 0) {
            return <div className="w-fit bg-gradient-to-r from-amber-100 to-yellow-100 text-yellow-800 font-semibold text-xs -ml-2 px-2 py-0.5 rounded-full">
                <span className="text-sm">${(domain.min_offer).toLocaleString('en', { useGrouping: true })} </span>
                <span>Minimum Offer</span>
            </div>
        } else if(domain.price <= 0 && domain.min_offer == 0) {
            return <span className="w-fit bg-gradient-to-r from-amber-100 to-yellow-100 text-yellow-800 font-semibold -ml-2 text-xs px-2 py-0.5 rounded-full">
                No Minimum Offer
            </span>
        } else {
            return `$${(domain.price).toLocaleString('en', { useGrouping: true })}`;
        }

        // if(domain.price > 0 && domain.min_offer > 0) {
        //     return domain.price
        // } else if(domain.price <= 0 && domain.min_offer > 0) {
        //     return <span className="-ml-3 bg-gradient-to-r from-amber-100 to-yellow-100 text-yellow-800 font-medium text-sm px-2 py-0.5 rounded-full">
        //         <span className="text-sm"> ${domain.min_offer.toLocaleString('en', { useGrouping: true })} </span>
        //         -
        //         <span className="text-xs"> Minimum Offer </span>
        //     </span>
        // } else if(domain.price <= 0 && domain.min_offer <= 0) {
        //     return <span className="-ml-3 bg-gradient-to-r from-amber-100 to-yellow-100 text-yellow-800 font-medium text-sm px-2 py-0.5 rounded-full">
        //         <span className="text-xs"> No Minimum Offer </span>
        //     </span>
        // }
    };

    if (searchResult == undefined) return <SearchDomainNow />;
    else if (searchResult.length == 0) return <SearchNoDomainFound />;

    const handleAddToCart = (domain) => {
        setIsLoading(true);

        if (domain.price) {
            const payload = {
                'user_id': auth.user.id,
                'tld_id': fees[domain.extension].tld_id,
                'name': domain.name,
                'price': domain.price,
                'is_fast_transfer': domain.fast_transfer ? 1 : 0,
                'vendor': 'afternic',
            };

            axios.post(route("mycart.market"), payload)
                .finally(() => {
                    setIsLoading(false);
                    toast.success('Added to cart');
                    setCart(prev => [...prev, domain.name]);
                    setCartCounter(cart.length + 1);
                })
        } else {
            onhandleAddToCart(domain.name)
        }
    }

    const getButton = (domain) => {
        if(domain.min_offer >= 0 && domain.price  <= 0) {
            return <button onClick={() => { showOffer(domain.name, domain.min_offer > 0 ? domain.min_offer : 100) }} className="disabled:opacity-50 px-4 py-2.5 flex items-center bg-gradient-to-r from-amber-100 to-yellow-100 text-yellow-900 border border-transparent rounded-md font-bold tracking-widest">
                Make Offer
            </button>
        } else if(domain.available && !cart?.includes(domain.name)) {
            return <PrimaryButton
                processing={isLoading}
                onClick={() => handleAddToCart(domain)}
                className="disabled:opacity-50 px-5 py-2.5  transition-all duration-200 font-medium flex items-center space-x-2"
            >
                <span>Add to Cart</span>
            </PrimaryButton>
        } else if(cart?.includes(domain.name)) {
            return <span className="block px-3.5 py-2.5 bg-gray-700 border border-transparent rounded-md font-semibold text-smtext-white tracking-widest bg-opacity-30">
                Added to cart
            </span>
        }
    }

    // Basic Search

    return (
        <div className="mx-auto container max-w-[900px] flex flex-col">

            <OfferPopUp modal={modal} showModal={showModal} offer={offer} min_offer={offerPrice} />

            <h2 className="text-lg font-semibold text-gray-800 border-b border-gray-100 pb-4 mb-6">Search Results</h2>

            <div className="space-y-4">
                {searchResult.map((domain, index) => (
                    <div
                        key={"sri-" + index}
                        className="bg-white border border-gray-100 rounded-2xl p-6 flex justify-between items-center hover:border-gray-200 transition-all duration-200 shadow-sm"
                    >
                        <div className="flex-1">
                            <h3 className="text-lg font-medium text-gray-800">
                                <div className="flex">
                                    {domain.name}
                                    {(isInteger(domain.price) || isInteger(domain.min_offer)) && (
                                        <span className="ml-2 bg-gradient-to-r from-amber-100 to-yellow-100 text-yellow-800 text-xs px-2 py-0.5 rounded-full font-medium border border-yellow-200 shadow-sm flex items-center">
                                            Premium
                                        </span>
                                    )}
                                    {domain.fast_transfer == 1 && (
                                        <span className="ml-2 bg-gradient-to-r bg-blue-50 text-primary text-xs px-2 py-0.5 rounded-full font-medium border border-yellow-200 shadow-sm flex items-center">
                                            Fast Transfer
                                        </span>
                                    )}
                                </div>
                            </h3>
                            {domain.available ? (
                                <span>{getDomainPrice(domain)}</span>
                                
                            ) : (
                                <p className="text-gray-500 mt-1.5 flex items-center">
                                    Not available
                                </p>
                            )}
                        </div>
                        
                        {getButton(domain)}
                    </div>
                ))}
            </div>
        </div>
    );
}