<?php

namespace App\Modules\Transfer\Services;

use App\Modules\Epp\Constants\EppDomainStatus;
use App\Modules\Transfer\Constants\TransferRequest;
use App\Modules\Transfer\Requests\ShowListRequest;
use App\Traits\CursorPaginate;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class TransferIndexQueryService
{
    use CursorPaginate;

    private $pageLimit = 20;

    public static function instance()
    {
        $transferIndexQueryService = new self;

        return $transferIndexQueryService;
    }

    public function getData(ShowListRequest $request, string $transaction)
    {
        $builder = $this->query();
        $this->filterByTransaction($builder, $transaction, $request->type);
        $this->whenHasStatus($builder, $request);
        $this->whenHasDomain($builder, $request);
        $this->whenHasOrderby($builder, $request);

        $builder = $builder->paginate($this->pageLimit);

        return CursorPaginate::cursor($builder, $this->paramToURI($request));
    }

    // PRIVATE Functions

    private function query(): Builder
    {
        return DB::table('transfer_domains')
            ->select('transfer_domains.*', 'domains.id as domain_id', 'domains.name as domain_name', 'user_contacts.user_id', 'registered_domains.status as registered_domain_status')
            ->join('registered_domains', 'registered_domains.id', '=', 'transfer_domains.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('user_contacts', function ($join) {
                $join->on('user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
                    ->where('user_contacts.user_id', '=', auth()->user()->id);
            });
    }

    private function filterByTransaction(Builder $builder, string $transaction, string $transferType): void
    {
        switch ($transaction) {
            case TransferRequest::PENDING: // Pending INBOUND transfer.
                $builder->whereIn('transfer_domains.status', [TransferRequest::PENDING_REQUEST, TransferRequest::PENDING_APPROVAL]);
                break;
            case TransferRequest::REQUIRED_ACTION: // Pending OUTBOUND transfer.
                $builder->where('transfer_domains.status', EppDomainStatus::TRANSFER_PENDING);
                break;
            case TransferRequest::CONFLICT: // Invalid INBOUND transfer.
                $builder->where('transfer_domains.status', EppDomainStatus::TRANSFER_CLIENT_CONFLICT);
                break;
            case TransferRequest::PREVIOUS: // INBOUND and OUTBOUND history.
                $builder->whereIn('transfer_domains.status', TransferRequest::TRANSFER_HISTORY_STATUS[$transferType]);
                break;
        }
    }

    private function whenHasStatus(Builder &$builder, ShowListRequest $request): void
    {
        $builder->when($request->has('status'), function (Builder $query) use ($request) {
            if (in_array($request->status, [
                'Client Approved',
                'Client Cancelled',
                'Client Rejected',
                'Server Approved',
                'System Cancelled'
            ])) {
                $request->status = $this->matchStatus($request->status);
                $status = $request->type . '.' . $request->status;
            } else {
                $status = $request->status;
            }
            $query->where('transfer_domains.status', $status);
        });
    }
    private function matchStatus($status)
    {
        return match ($status) {
            'Client Approved' => EppDomainStatus::TRANSFER_CLIENT_APPROVED,
            'Client Cancelled' => EppDomainStatus::TRANSFER_CLIENT_CANCELLED,
            'Client Rejected' => EppDomainStatus::TRANSFER_CLIENT_REJECTED,
            'Server Approved' => EppDomainStatus::TRANSFER_SERVER_APPROVED,
            'System Cancelled' => TransferRequest::SYSTEM_CANCELLED,
        };
    }

    private function whenHasDomain(Builder &$builder, ShowListRequest $request): void
    {
        $builder->when($request->has('domain'), function (Builder $query) use ($request) {
            $domain = $request->domain;
            $query->where('domains.name', 'like', $domain . '%');
        });
    }

    private static function whenHasOrderby(Builder &$builder, ShowListRequest $request): void
    {
        $builder->when($request->has('orderby'), function (Builder $query) use ($request) {
            self::setCustomOrderBy($query, $request);
        })
            ->when(! $request->has('orderby'), function (Builder $query) {
                $query->orderBy('transfer_domains.id', 'desc');
            });
    }

    private static function setCustomOrderBy(Builder $query, ShowListRequest $request): void
    {
        $orderby = explode(':', $request->orderby);

        if (count($orderby) == 2 && in_array($orderby[1], [' Asc', ' Desc'])) {
            $column = [
                'Requested At' => 'transfer_domains.created_at',
                'Last Update' => 'transfer_domains.updated_at',
                'name' => 'domains.name',
                'Domain' => 'domains.name',
            ][$orderby[0]] ?? 'transfer_domains.id';

            $query->orderBy($column, trim($orderby[1]));
        } else {
            $query->orderBy('transfer_domains.id', 'desc');
        }
    }

    private function paramToURI(ShowListRequest $request): array
    {
        $param = [];

        if ($request->has('transaction')) {
            $param[] = 'transaction=' . $request->transaction;
        }
        if ($request->has('domain')) {
            $param[] = 'domain=' . $request->registry;
        }
        if ($request->has('orderby')) {
            $param[] = 'orderby=' . $request->orderby;
        }

        return $param;
    }
}
