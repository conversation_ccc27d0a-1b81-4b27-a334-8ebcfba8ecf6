//* PACKAGES
import React, { useState, useEffect } from 'react';

//* ICONS
//...

//* LAYOUTS
import AccountCenterLayout from "@/Layouts/AccountCenterLayout";

//* COMPONENTS
import PrimaryButton from '@/Components/PrimaryButton';
import PaymentMethodCardDetailsComponent from '@/Components/PaymentMethod/PaymentMethodCardDetailsComponent';
import AppListEmptyComponent from '@/Components/App/AppListEmptyComponent';

//* STATE
//...

//* UTILS
//...

//* CONSTANT
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
import PartialModalPaymentMethodFormCreate from './Partials/PartialModalPaymentMethodFormCreate';
import PartialModalSelectPrimaryPaymentMethod from './Partials/PartialModalSelectPrimaryPaymentMethod';

export default function PaymentMethodListPage(
    {
        //! VARIABLES
        items,
        defaultItem,
        publicKey,
        primaryPaymentMethod,
        user

        //! STATES
        //...

        //! EVENTS
        //..,
    }
) {
    //! PACKAGE
    //...

    //! STATES
    const [stateModalOpenCreate, setStateModalOpenCreate] = useState(false);
    const [stateModalOpenSelectPrimaryPaymentMethod, setStateModelOpenSelectPrimaryPaymentMethod] = useState(false);
    const [activeTab, setActiveTab] = useState('payment-methods');

    //! USE EFFECTS
    //...
    //! FUNCTIONS
    //...

    return (
        <AccountCenterLayout
            postRouteName={"payment-method.index"}
        >
            <PartialModalPaymentMethodFormCreate
                publicKey={publicKey}
                isModalOpen={stateModalOpenCreate}
                handleModalClose={() => setStateModalOpenCreate(false)}
            />

            <PartialModalSelectPrimaryPaymentMethod
                primaryPaymentMethodRetrieved={primaryPaymentMethod}
                isModalOpen={stateModalOpenSelectPrimaryPaymentMethod}
                handleModalClose={() => setStateModelOpenSelectPrimaryPaymentMethod(false)}
            />
            <div
                className="mx-auto container max-w-[900px] mt-10 flex flex-col gap-8"
            >
                {/* Tab Navigation */}
                <div className="border-b border-gray-200">
                    <nav className="flex space-x-8">
                        <button
                            onClick={() => setActiveTab('payment-methods')}
                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                activeTab === 'payment-methods'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }`}
                        >
                            Payment Methods
                        </button>
                        {/* <button
                            onClick={() => setActiveTab('auto-renewal-status')}
                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                activeTab === 'auto-renewal-status'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }`}
                        >
                            Auto-Renewal Status
                        </button> */}
                    </nav>
                </div>

                {activeTab === 'payment-methods' && (
                    <>
                        <div
                            className='flex items-center justify-between'
                        >
                            <div
                                className='text-2xl font-bold'
                            >
                                Payment Methods
                            </div>
                            <div className='flex space-x-3'>
                                <PrimaryButton
                                    onClick={() => setStateModelOpenSelectPrimaryPaymentMethod(true)}
                                >
                                    Add Primary Payment Method
                                </PrimaryButton>
                                <PrimaryButton
                                    onClick={() => setStateModalOpenCreate(true)}
                                >
                                    Add Payment Method
                                </PrimaryButton>
                            </div>
                        </div>

                        <hr />

                        {
                            items.length == 0
                                ?
                                <AppListEmptyComponent
                                    customMessage='No Payment Methods Found'
                                />
                                :
                                <div className="flex flex-col gap-8">
                                    {
                                        items.map(
                                            (item, index) => {
                                                let isDefault = false;

                                                if (defaultItem != null) {
                                                    if (item.id == defaultItem.payment_method_id) {
                                                        isDefault = true;
                                                    }
                                                }

                                                return (
                                                    <PaymentMethodCardDetailsComponent
                                                        key={index}
                                                        item={item}
                                                        isDefault={isDefault}
                                                        primaryPaymentMethod = {primaryPaymentMethod}
                                                    />
                                                );
                                            }
                                        )
                                    }
                                </div>
                        }
                    </>
                )}

                {activeTab === 'auto-renewal-status' && (
                    <PaymentMethodStatus
                        userId={user?.id}
                    />
                )}
            </div>
        </AccountCenterLayout>
    );
}
