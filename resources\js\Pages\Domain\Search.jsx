import React, { useState, useEffect } from "react";
import UserLayout from "@/Layouts/UserLayout";

import SearchResults from "@/Components/Domain/Search/SearchResults";
import AiSearchResults from "@/Components/Domain/Search/AiSearchResults";
import MarketPlaceResults from "@/Components/Domain/Search/MarketPlaceResults";
import SearchDomainNow from "@/Components/Domain/Search/SearchDomainNow";
import SearchRequest from "@/Components/Domain/Search/SearchRequest";
import { router } from "@inertiajs/react";
import LoaderSpinner from "@/Components/LoaderSpinner";
import { toast } from "react-toastify";
import CartCounterState from "@/State/CartCounterState";

export default function Search({ auth, data, basic_cart, market_cart, comp_id, cartCount, fees, transfer_fees }) {

    const [cart, setCart] = useState([]);
    const [counter, setCounter] = useState(-1);
    const [basicData, setBasicData] = useState([]);

    const [haserror, setHasError] = useState(false);
    const [gSearch, setGSearch] = useState('');
    const [hasSpinner, setSpinner] = useState(false);
    const [currentTab, setCurrentTab] = useState('basic');
    const [aiCategories, setAiCategories] = useState([]);

    const [offer, setOffer] = useState('');
    const [modal, showModal] = useState(false);
    const [offerPrice, setOfferPrice] = useState(100);

    const [marketFilter, setMarketFilter] = useState({
        'perPage' : 100,
        'currPage' : 1,
        'lastPage' : null,
        'total': 1
    });

    const [tabResults, setTabResults] = useState({
        basic: undefined,
        ai: data || undefined,
        market: undefined
    });
    
    const [searchingTab, setSearchingTab] = useState('basic');

    const handleCategoriesUpdate = (categories) => {
        setAiCategories(categories);
    };

    const handleSearch = async (formData) => {
        if(counter >= 0 || hasSpinner) return;

        setHasError(false);
        setSearchingTab(currentTab);

        if(currentTab === 'ai') {
            setSpinner(true);
            setTabResults(prev => ({ ...prev, ai: [] }));

            axios.post(route('search.ai'), formData)
            .then((a) => {
                if(a.data['status'] && a.data['status'] == 'error') handleError({})
                else setTabResults(prev => ({ ...prev, ai: a.data }));
            })
            .catch((err) => {
                handleError(err)
            })
            .finally(() => {
                setSpinner(false)

                if(location.href.includes('?')) history.pushState({}, null, location.href.split('?')[0]);
            })
        } else if (currentTab === 'basic') {
            setSpinner(true);
            setTabResults(prev => ({ ...prev, basic: [] }));

            let basic_data = [];
            let market_basic_data = [];

            await axios.post(route('domain.search.basic'), { domain: formData.domains })
            .then((a) => {
                market_basic_data = a.data.output;
            })
            .catch((err) => {
                //
            })

            await axios.post(route('domain.check'), { domains: formData.domains, extensions: formData.extensions, })
            .then((a) => {
                basic_data = a.data.data;
            })
            .catch((err) => {
                handleError(err)
            })
            .finally(() => {
                setSpinner(false)
                if(location.href.includes('?')) history.pushState({}, null, location.href.split('?')[0]);
            })

            if(market_basic_data != 0) {
                basic_data = basic_data.map(item => {
                    if (item.name === `${market_basic_data.domain}.${market_basic_data.extension}`) {
                        return {
                            ...item,
                            price: market_basic_data.price,
                            has_digit: market_basic_data.has_digit,
                            extension: market_basic_data.extension,
                            has_hypen: market_basic_data.has_hypen,
                            available: true,
                            min_offer: market_basic_data.min_offer,
                            fast_transfer: market_basic_data.fast_transfer,
                            in_afternic: true
                        };
                    }
                    return item;
                });
            }

            setTabResults(prev => ({ ...prev, basic: basic_data }));

        } else if (currentTab === 'market') {
            doMarketSearch(formData.domains)
        }
    };

    const doMarketSearch = (domains, filters=null) => {
        setSpinner(true);
        setBasicData([]);

        setTabResults(prev => ({ ...prev, market: [] }));

        const tldRegex = /^[a-zA-Z0-9-]+(\.[a-z]{2,})+$/i;

        const { withTLD, keywords } = domains
        .split(/[\s,]+/)
        .filter(Boolean)
        .reduce((acc, word) => {
            if (tldRegex.test(word)) {
                acc.withTLD.push(word);
                acc.keywords.push(word.replace(/\.[a-z]{2,}$/i, ''));
            } else acc.keywords.push(word);
            return acc;
        }, { withTLD: [], keywords: [] });

        if(withTLD.length > 0) {
            withTLD.forEach(async (a) => {
                await axios.post(route('domain.search.basic'), { domain: a })
                .then((b) => {
                    setBasicData(prev => ([...prev, b.data.output]))
                })
            })
        }

        axios.post(route('domain.search.market'), { domains: keywords.toString(), perPage: filters != null ? filters.perPage : 100, page: filters != null ? filters.page : 1 })
        .then((a) => {
            setMarketFilter({...marketFilter, currPage: a.data.current_page, lastPage: a.data.last_page, total: a.data.total_count, perPage: a.data.per_page})
            setTabResults(prev => ({ ...prev, market: a.data.domains }));
        })
        .catch((err) => {
            handleError(err)
        })
        .finally(() => {
            setSpinner(false)
            if(location.href.includes('?')) history.pushState({}, null, location.href.split('?')[0]);
        })
    }

    const handleError = (error) => {
        if(error.response && error.response.headers['retry-after']) {
            setCounter(parseInt(error.response.headers['retry-after']))
            toast.error(error.response.data['message'] + " retry after " + getformatTimer(error.response.headers['retry-after']))
        } else {
            setHasError(true);
        }
    }

    const handleTabChange = (newTab) => {
        setTabResults(prev => ({
            ...prev,
            [newTab]: undefined
        }));

        setCurrentTab(newTab);
    };

    const getformatTimer = (sec) => {
        let hours = Math.floor(sec / 3600);
        sec %= 3600;
        let minutes = Math.floor(sec / 60);
        let seconds = sec % 60;

        return `${hours > 0 ? `${String(hours).padStart(2, '0')}hs` : ''} ${minutes > 0 ? `${String(minutes).padStart(2, '0')}min` : ''} ${String(seconds).padStart(2, '0')}s`;
    }

    const showOffer = (domain, price) => {
        setOffer(domain);
        setOfferPrice(price);
        showModal(true);
    }

    const renderContent = () => {
        if(hasSpinner) {
            return (
                <div className="mx-auto w-full max-w-[900px]  container mt-16 flex flex-col px-80 rounded-lg">
                    <div className="w-full h-full flex justify-center">
                        <LoaderSpinner ml='ml-0' h='h-12' w='w-12' position='absolute' />
                    </div>
                    <div className="w-full h-full flex justify-center">
                        <span className="relative top-16">Loading Data . . .</span>
                    </div>
                </div>
            );
        }

        if(counter >= 0) {
            return (
                <div className="mx-auto container mt-5 w-full max-w-[900px] text-red-500 rounded-lg">
                    <span>Rate limit reached, try again after <span className="font-semibold"> {getformatTimer(counter)}</span></span>
                </div>
            );
        }

        if(haserror) {
            return (
                <div className="mx-auto container w-full max-w-[900px]  mt-5 text-red-500 rounded-lg">
                    <span>Service unavailable, please try again later.</span>
                </div>
            );
        }

        const results = tabResults[currentTab];

        switch(currentTab) {
            case 'basic':
                return (
                    <SearchResults
                        key={'sr-basic-' + comp_id}
                        searchResult={results}
                        hasSpinner={hasSpinner}
                        cart={cart}
                        auth={auth}
                        fees={fees}
                        setCart={setCart}
                        offer={offer}
                        showOffer={showOffer}
                        offerPrice={offerPrice}
                        setOfferPrice={setOfferPrice}
                        modal={modal}
                        showModal={showModal}
                    />
                );
            case 'ai':
                return (
                    <AiSearchResults 
                        key={'sr-ai-' + comp_id}
                        searchResult={results}
                        fees={transfer_fees}
                        auth={auth}
                        cart={cart}
                        setCart={setCart}
                        modal={modal}
                        showModal={showModal}
                        offer={offer}
                        showOffer={showOffer}
                        offerPrice={offerPrice}
                        setOfferPrice={setOfferPrice}
                        onCategoriesUpdate={handleCategoriesUpdate}
                    />
                );
            case 'market':
                return (
                    <MarketPlaceResults
                        key={'sr-market-' + comp_id}
                        searchResult={results}
                        auth={auth}
                        fees={transfer_fees}
                        marketFilter={marketFilter}
                        setMarketFilter={setMarketFilter}
                        gSearch={gSearch}
                        doMarketSearch={doMarketSearch}
                        cart={cart}
                        modal={modal}
                        showModal={showModal}
                        offer={offer}
                        showOffer={showOffer}
                        offerPrice={offerPrice}
                        setOfferPrice={setOfferPrice}
                        setCart={setCart}
                        basicData={basicData}
                    />
                );
            default:
                return <SearchDomainNow />;
        }
    };

    useEffect(() => {
        if (data) setTabResults(prev => ({ ...prev, [searchingTab]: data }));
    }, [data, searchingTab]);

    useEffect(() => {
        if(counter == 0) return setCounter(-1);
        counter > 0 && setTimeout(() => setCounter(counter - 1), 1000);
    }, [counter]);

    useEffect(() => {
        router.on("start", () => setSpinner(true));
        router.on("finish", () => setSpinner(false));
    }, []);

    useEffect(() => {
        const cartDomains = basic_cart.cart.map(item => item.name);
        const domainList = market_cart.domains.map(item => item.name);

        setCart([...cartDomains, ...domainList]);
    }, []);

    return (
        <UserLayout>
            <>
                <SearchRequest 
                    activeTab={currentTab}
                    onTabChange={handleTabChange}
                    onSearch={handleSearch}
                    gSearch={gSearch}
                    setGSearch={setGSearch}
                    searchResult={tabResults[currentTab]}
                    aiCategories={aiCategories}
                    hasSpinner={hasSpinner}
                    counter={counter}
                    marketFilter={marketFilter}
                    setMarketFilter={setMarketFilter}
                />
                {renderContent()}
            </>
        </UserLayout>
    );
}